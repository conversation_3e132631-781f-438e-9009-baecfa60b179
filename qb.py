#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QuickBuild 编译统计工具
用于统计从固定日期开始编译失败到编译成功的时间消耗
"""

import requests
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
import json
import argparse
import sys
import logging
from typing import List, Dict, Optional, Tuple
import time


class QuickBuildAPI:
    """QuickBuild RESTful API 客户端"""

    def __init__(self, base_url: str, username: str, password: str, debug: bool = False):
        """
        初始化 QuickBuild API 客户端

        Args:
            base_url: QuickBuild 服务器地址，例如 http://localhost:8810
            username: 用户名
            password: 密码
            debug: 是否启用调试模式
        """
        self.base_url = base_url.rstrip('/')
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
        self.debug = debug
        self.logger = logging.getLogger(__name__)

        if debug:
            self.logger.info(f"初始化 QuickBuild API 客户端: {self.base_url}")
            self.logger.info(f"用户名: {username}")

    def search_builds(self, configuration_id: Optional[int] = None,
                     from_date: Optional[str] = None,
                     to_date: Optional[str] = None,
                     status: Optional[str] = None,
                     count: int = 1000,
                     first: int = 0) -> List[Dict]:
        """
        搜索构建记录

        Args:
            configuration_id: 配置ID
            from_date: 开始日期 (yyyy-MM-dd)
            to_date: 结束日期 (yyyy-MM-dd)
            status: 构建状态 (SUCCESSFUL, FAILED, CANCELLED, RUNNING, TIMEOUT)
            count: 返回数量
            first: 起始位置

        Returns:
            构建记录列表
        """
        url = f"{self.base_url}/rest/builds"
        params = {
            'count': count,
            'first': first
        }

        if configuration_id:
            params['configuration_id'] = configuration_id
        if from_date:
            params['from_date'] = from_date
        if to_date:
            params['to_date'] = to_date
        if status:
            params['status'] = status

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()

            # 解析XML响应
            root = ET.fromstring(response.content)
            builds = []

            for build_elem in root.findall('.//build'):
                build_data = {}
                for child in build_elem:
                    if child.tag in ['id', 'configuration', 'version', 'status', 'beginDate', 'duration']:
                        build_data[child.tag] = child.text
                builds.append(build_data)

            return builds

        except requests.RequestException as e:
            print(f"搜索构建记录失败: {e}")
            return []
        except ET.ParseError as e:
            print(f"解析XML响应失败: {e}")
            return []

    def get_build_details(self, build_id: int) -> Optional[Dict]:
        """
        获取构建详细信息

        Args:
            build_id: 构建ID

        Returns:
            构建详细信息
        """
        url = f"{self.base_url}/rest/builds/{build_id}"

        try:
            response = self.session.get(url)
            response.raise_for_status()

            root = ET.fromstring(response.content)
            build_data = {}

            for child in root:
                build_data[child.tag] = child.text

            return build_data

        except requests.RequestException as e:
            print(f"获取构建详情失败 (ID: {build_id}): {e}")
            return None
        except ET.ParseError as e:
            print(f"解析构建详情XML失败 (ID: {build_id}): {e}")
            return None


class BuildAnalyzer:
    """构建分析器"""

    def __init__(self, api: QuickBuildAPI):
        self.api = api

    def analyze_failure_to_success_time(self, configuration_id: Optional[int] = None,
                                      start_date: str = None,
                                      end_date: str = None) -> Dict:
        """
        分析从编译失败到编译成功的时间消耗

        Args:
            configuration_id: 配置ID
            start_date: 分析开始日期 (yyyy-MM-dd)
            end_date: 分析结束日期 (yyyy-MM-dd)

        Returns:
            分析结果
        """
        print(f"开始分析从 {start_date} 到 {end_date} 的构建数据...")

        # 获取所有构建记录
        all_builds = self.api.search_builds(
            configuration_id=configuration_id,
            from_date=start_date,
            to_date=end_date,
            count=10000
        )

        if not all_builds:
            print("未找到构建记录")
            return {}

        print(f"找到 {len(all_builds)} 条构建记录")

        # 按时间排序
        all_builds.sort(key=lambda x: int(x.get('beginDate', 0)))

        # 分析失败到成功的时间间隔
        failure_to_success_intervals = []
        current_failure_start = None

        for build in all_builds:
            build_id = build.get('id')
            status = build.get('status')
            begin_date = int(build.get('beginDate', 0))
            begin_datetime = datetime.fromtimestamp(begin_date / 1000)

            print(f"处理构建 {build_id}: {status} at {begin_datetime}")

            if status == 'FAILED':
                if current_failure_start is None:
                    current_failure_start = begin_datetime
                    print(f"  -> 失败序列开始: {current_failure_start}")
            elif status == 'SUCCESSFUL':
                if current_failure_start is not None:
                    # 计算从失败开始到成功的时间间隔
                    time_to_success = begin_datetime - current_failure_start
                    failure_to_success_intervals.append({
                        'failure_start': current_failure_start,
                        'success_time': begin_datetime,
                        'duration_hours': time_to_success.total_seconds() / 3600,
                        'duration_days': time_to_success.days,
                        'build_id': build_id
                    })
                    print(f"  -> 成功恢复: 耗时 {time_to_success}")
                    current_failure_start = None

        # 统计分析
        if not failure_to_success_intervals:
            return {
                'total_intervals': 0,
                'message': '在指定时间范围内未发现从失败到成功的完整周期'
            }

        durations_hours = [interval['duration_hours'] for interval in failure_to_success_intervals]

        analysis_result = {
            'total_intervals': len(failure_to_success_intervals),
            'intervals': failure_to_success_intervals,
            'statistics': {
                'min_hours': min(durations_hours),
                'max_hours': max(durations_hours),
                'avg_hours': sum(durations_hours) / len(durations_hours),
                'total_failure_time_hours': sum(durations_hours)
            }
        }

        return analysis_result

    def print_analysis_report(self, analysis_result: Dict):
        """打印分析报告"""
        if not analysis_result or analysis_result.get('total_intervals', 0) == 0:
            print("\n=== 分析报告 ===")
            print(analysis_result.get('message', '无数据'))
            return

        stats = analysis_result['statistics']
        intervals = analysis_result['intervals']

        print("\n=== 编译失败到成功时间消耗分析报告 ===")
        print(f"分析周期数: {analysis_result['total_intervals']}")
        print(f"最短恢复时间: {stats['min_hours']:.2f} 小时")
        print(f"最长恢复时间: {stats['max_hours']:.2f} 小时")
        print(f"平均恢复时间: {stats['avg_hours']:.2f} 小时")
        print(f"总失败时间: {stats['total_failure_time_hours']:.2f} 小时")

        print("\n=== 详细记录 ===")
        for i, interval in enumerate(intervals, 1):
            print(f"{i}. 失败开始: {interval['failure_start'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   成功时间: {interval['success_time'].strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   恢复耗时: {interval['duration_hours']:.2f} 小时 ({interval['duration_days']} 天)")
            print(f"   成功构建ID: {interval['build_id']}")
            print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='QuickBuild 编译统计工具')
    parser.add_argument('--server', required=True, help='QuickBuild 服务器地址')
    parser.add_argument('--username', required=True, help='用户名')
    parser.add_argument('--password', required=True, help='密码')
    parser.add_argument('--config-id', type=int, help='配置ID')
    parser.add_argument('--start-date', required=True, help='开始日期 (yyyy-MM-dd)')
    parser.add_argument('--end-date', help='结束日期 (yyyy-MM-dd)，默认为今天')
    parser.add_argument('--output', help='输出JSON文件路径')

    args = parser.parse_args()

    # 默认结束日期为今天
    end_date = args.end_date or datetime.now().strftime('%Y-%m-%d')

    # 创建API客户端
    api = QuickBuildAPI(args.server, args.username, args.password)

    # 创建分析器
    analyzer = BuildAnalyzer(api)

    # 执行分析
    result = analyzer.analyze_failure_to_success_time(
        configuration_id=args.config_id,
        start_date=args.start_date,
        end_date=end_date
    )

    # 打印报告
    analyzer.print_analysis_report(result)

    # 保存到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2, default=str)
        print(f"\n分析结果已保存到: {args.output}")


if __name__ == '__main__':
    main()