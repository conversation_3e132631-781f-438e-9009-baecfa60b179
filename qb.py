import os
from datetime import datetime
from quickbuild import QBClient

# -----------------------------------------------------------------------------
# 请根据您的 QuickBuild 环境修改以下配置
# -----------------------------------------------------------------------------
QB_URL = 'https://qb.ygame.work/'
QB_USER = 'chenjiehao'
QB_PASSWORD = 'Sk@86980820'
CONFIGURATION_ID = 4876  # 请替换为您的构建配置 ID
START_DATE = '2025-07-01'  # 您希望开始统计的日期
# -----------------------------------------------------------------------------

def get_time_from_failed_to_success():
    """
    计算从失败的构建到成功的构建所花费的时间。
    """
    client = QBClient(QB_URL, QB_USER, QB_PASSWORD)

    # 将开始日期转换为 datetime 对象
    from_date = datetime.strptime(START_DATE, '%Y-%m-%d').date()

    # 获取从指定日期开始的所有构建
    # 注意：这里我们假设 API 或客户端按时间顺序列出构建
    builds = client.builds.get(configuration_id=CONFIGURATION_ID, from_date=from_date)

    if not builds:
        print("在指定日期后没有找到任何构建。")
        return

    failed_build_start_time = None
    last_failed_build_id = None

    for build in builds:
        build_id = build['id']
        status = client.builds.get_status(build_id)
        
        print(f"正在处理 Build #{build_id}, 状态: {status}")

        if status == 'SUCCESSFUL':
            if failed_build_start_time:
                # 获取成功的构建和第一个失败的构建的详细信息
                successful_build = client.builds.get_basic(build_id)
                failed_build = client.builds.get_basic(last_failed_build_id)

                # 获取开始时间
                success_time = datetime.fromisoformat(successful_build['beginDate'])
                
                # 计算时间差
                time_diff = success_time - failed_build_start_time
                print(f"--------------------------------------------------")
                print(f"检测到一次修复！")
                print(f"  从失败的构建 #{last_failed_build_id} 到成功的构建 #{build_id}")
                print(f"  总共耗时: {time_diff}")
                print(f"--------------------------------------------------")

                # 重置失败构建的开始时间
                failed_build_start_time = None
                last_failed_build_id = None

        elif status == 'FAILED':
            if not failed_build_start_time:
                # 这是这个失败序列中的第一个失败构建
                failed_build = client.builds.get_basic(build_id)
                failed_build_start_time = datetime.fromisoformat(failed_build['beginDate'])
                last_failed_build_id = build_id
                print(f"检测到失败的构建 #{build_id} at {failed_build_start_time}, 等待成功的构建...")


if __name__ == '__main__':
    get_time_from_failed_to_success()